import { NextRequest, NextResponse } from 'next/server';
import { deleteNotification, getNotificationById } from '@/lib/database';

/**
 * POST /api/notifications/bulk-delete - Delete multiple notifications
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { notificationIds } = body;

    if (!Array.isArray(notificationIds) || notificationIds.length === 0) {
      return NextResponse.json(
        { error: 'Invalid notification IDs array' },
        { status: 400 }
      );
    }

    // Validate all IDs are numbers
    const validIds = notificationIds.filter(id => !isNaN(parseInt(id)));
    if (validIds.length !== notificationIds.length) {
      return NextResponse.json(
        { error: 'All notification IDs must be valid numbers' },
        { status: 400 }
      );
    }

    // Delete notifications one by one
    const deletedIds = [];
    const failedIds = [];

    for (const id of validIds) {
      try {
        const notificationId = parseInt(id);
        
        // Check if notification exists
        const notification = await getNotificationById(notificationId);
        if (notification) {
          await deleteNotification(notificationId);
          deletedIds.push(notificationId);
        } else {
          failedIds.push(notificationId);
        }
      } catch (error) {
        console.error(`Error deleting notification ${id}:`, error);
        failedIds.push(parseInt(id));
      }
    }

    return NextResponse.json(
      { 
        message: `Successfully deleted ${deletedIds.length} notifications`,
        deletedIds,
        failedIds,
        totalRequested: notificationIds.length,
        totalDeleted: deletedIds.length,
        totalFailed: failedIds.length
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Error in bulk delete notifications:', error);
    return NextResponse.json(
      { error: 'Failed to delete notifications' },
      { status: 500 }
    );
  }
}
