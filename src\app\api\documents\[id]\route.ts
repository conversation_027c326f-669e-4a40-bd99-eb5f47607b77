import { NextRequest, NextResponse } from 'next/server';
import { getDocumentById, deleteDocument, runQuery } from '@/lib/database';

/**
 * DELETE /api/documents/[id] - Delete a document
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const documentId = parseInt(id);
    
    if (isNaN(documentId)) {
      return NextResponse.json(
        { error: 'Invalid document ID' },
        { status: 400 }
      );
    }

    // Get document from database first to verify it exists
    const document = await getDocumentById(documentId);
    
    if (!document) {
      return NextResponse.json(
        { error: 'Document not found' },
        { status: 404 }
      );
    }

    // Delete document from database
    await deleteDocument(documentId);

    return NextResponse.json(
      { 
        message: 'Document deleted successfully',
        documentId,
        documentName: document.document_name
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Error deleting document:', error);
    return NextResponse.json(
      { error: 'Failed to delete document' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/documents/[id] - Get a specific document by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const documentId = parseInt(id);
    
    if (isNaN(documentId)) {
      return NextResponse.json(
        { error: 'Invalid document ID' },
        { status: 400 }
      );
    }

    const document = await getDocumentById(documentId);
    
    if (!document) {
      return NextResponse.json(
        { error: 'Document not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(document);

  } catch (error) {
    console.error('Error fetching document:', error);
    return NextResponse.json(
      { error: 'Failed to fetch document' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/documents/[id] - Update a document
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const documentId = parseInt(id);

    if (isNaN(documentId)) {
      return NextResponse.json(
        { error: 'Invalid document ID' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { applicant_name, status, document_data, approved_at } = body;

    // Get document from database first to verify it exists
    const document = await getDocumentById(documentId);

    if (!document) {
      return NextResponse.json(
        { error: 'Document not found' },
        { status: 404 }
      );
    }

    // Build update query dynamically based on provided fields
    const updateFields: string[] = [];
    const updateValues: any[] = [];

    if (applicant_name !== undefined) {
      updateFields.push('applicant_name = ?');
      updateValues.push(applicant_name);
    }

    if (status !== undefined) {
      updateFields.push('status = ?');
      updateValues.push(status);
    }

    if (document_data !== undefined) {
      updateFields.push('document_data = ?');
      updateValues.push(Buffer.from(document_data));
    }

    if (approved_at !== undefined) {
      updateFields.push('approved_at = ?');
      updateValues.push(approved_at);
    }

    if (updateFields.length === 0) {
      return NextResponse.json(
        { error: 'No fields to update' },
        { status: 400 }
      );
    }

    // Add document ID to the end of values array
    updateValues.push(documentId);

    // Execute update query
    await runQuery(
      `UPDATE documents SET ${updateFields.join(', ')} WHERE id = ?`,
      updateValues
    );

    // Get updated document
    const updatedDocument = await getDocumentById(documentId);

    return NextResponse.json(
      {
        message: 'Document updated successfully',
        document: updatedDocument
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Error updating document:', error);
    return NextResponse.json(
      { error: 'Failed to update document' },
      { status: 500 }
    );
  }
}
