#!/usr/bin/env node

/**
 * Database test script for LDIS Archives Table
 * This script tests the archives table functionality
 */

require("ts-node/register");

async function testArchivesDatabase() {
  try {
    console.log("🧪 Testing LDIS Archives Database...\n");

    // Import the database module
    const {
      initializeDatabase,
      createArchive,
      getArchiveById,
      getArchivesByUserId,
      getAllArchives,
      getArchivesByDocumentId,
      updateArchiveApproval,
      deleteArchive,
      getArchivesWithUserInfo,
      getArchivesByApplicantName,
      getArchivesByDocumentName,
      createDocument,
      getAllUsers,
      closeDatabase,
    } = require("../src/lib/database.ts");

    // Initialize the database
    console.log("📦 Initializing database...");
    await initializeDatabase();
    console.log("✅ Database initialized\n");

    // Get existing users
    console.log("👥 Getting users...");
    const users = await getAllUsers();
    if (users.length === 0) {
      console.log("❌ No users found. Please create a user first.");
      return;
    }
    const testUser = users[0];
    console.log(
      `✅ Using test user: ${testUser.username} (ID: ${testUser.id})\n`
    );

    // Create a test document first (needed for foreign key)
    console.log("📄 Creating test document...");
    const documentId = await createDocument(
      "Test Certificate",
      "John Doe",
      Buffer.from("Test document data"),
      "approved",
      testUser.id
    );
    console.log(`✅ Test document created with ID: ${documentId}\n`);

    // Test creating an archive
    console.log("📦 Creating archive...");
    const archiveId = await createArchive(
      "Test Certificate",
      "John Doe",
      Buffer.from("Archived document data"),
      testUser.id,
      documentId,
      new Date().toISOString(),
      testUser.id
    );
    console.log(`✅ Archive created with ID: ${archiveId}\n`);

    // Test getting archive by ID
    console.log("🔍 Getting archive by ID...");
    const archive = await getArchiveById(archiveId);
    console.log(
      `✅ Archive found: ${archive?.document_name} for ${archive?.applicant_name}\n`
    );

    // Test getting archives by user ID
    console.log("👤 Getting archives by user ID...");
    const userArchives = await getArchivesByUserId(testUser.id);
    console.log(`✅ Found ${userArchives.length} archives for user\n`);

    // Test getting all archives
    console.log("📋 Getting all archives...");
    const allArchives = await getAllArchives();
    console.log(`✅ Found ${allArchives.length} total archives\n`);

    // Test getting archives by document ID
    console.log("📄 Getting archives by document ID...");
    const docArchives = await getArchivesByDocumentId(documentId);
    console.log(`✅ Found ${docArchives.length} archives for document\n`);

    // Test getting archives by applicant name
    console.log("👨 Getting archives by applicant name...");
    const applicantArchives = await getArchivesByApplicantName("John Doe");
    console.log(`✅ Found ${applicantArchives.length} archives for John Doe\n`);

    // Test getting archives by document name
    console.log("📋 Getting archives by document name...");
    const nameArchives = await getArchivesByDocumentName("Test Certificate");
    console.log(
      `✅ Found ${nameArchives.length} archives for Test Certificate\n`
    );

    // Test updating archive approval
    console.log("✅ Updating archive approval...");
    await updateArchiveApproval(
      archiveId,
      new Date().toISOString(),
      testUser.id
    );
    const updatedArchive = await getArchiveById(archiveId);
    console.log(
      `✅ Archive approval updated: approved_by = ${updatedArchive?.approved_by}\n`
    );

    // Test getting archives with user info
    console.log("👥 Getting archives with user info...");
    const archivesWithInfo = await getArchivesWithUserInfo();
    console.log(`✅ Found ${archivesWithInfo.length} archives with user info`);
    if (archivesWithInfo.length > 0) {
      console.log(
        `   First archive: ${archivesWithInfo[0].document_name} by ${archivesWithInfo[0].username}\n`
      );
    }

    // Test deleting archive
    console.log("🗑️ Deleting test archive...");
    await deleteArchive(archiveId);
    const deletedArchive = await getArchiveById(archiveId);
    console.log(
      `✅ Archive deleted: ${
        deletedArchive ? "Still exists" : "Successfully deleted"
      }\n`
    );

    console.log("🎉 All archive database tests completed successfully!");

    // Close the database connection
    await closeDatabase();
  } catch (error) {
    console.error("❌ Error testing archives database:", error);
    process.exit(1);
  }
}

// Run the test
testArchivesDatabase();
