import { NextRequest, NextResponse } from 'next/server';
import { getUserByUsername, updateUserPassword } from '@/lib/database';
import bcrypt from 'bcryptjs';

/**
 * POST /api/auth/reset-password - Reset user password using recovery key
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { username, recoveryKey, newPassword } = body;
    
    // Validate required fields
    if (!username || !recoveryKey || !newPassword) {
      return NextResponse.json(
        { error: 'Username, recovery key, and new password are required' },
        { status: 400 }
      );
    }
    
    // Get user from database
    const user = await getUserByUsername(username);
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }
    
    // Verify recovery key
    if (user.recovery_key !== recoveryKey) {
      return NextResponse.json(
        { error: 'Invalid recovery key' },
        { status: 401 }
      );
    }
    
    // Validate new password strength
    if (newPassword.length < 6) {
      return NextResponse.json(
        { error: 'New password must be at least 6 characters long' },
        { status: 400 }
      );
    }
    
    // Hash the new password
    const hashedPassword = await bcrypt.hash(newPassword, 12);
    
    // Update the user's password
    await updateUserPassword(user.id, hashedPassword);
    
    return NextResponse.json(
      { 
        message: 'Password reset successful',
        user: {
          id: user.id,
          username: user.username
        }
      },
      { status: 200 }
    );
    
  } catch (error) {
    console.error('Error during password reset:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
