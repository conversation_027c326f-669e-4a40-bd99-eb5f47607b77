#!/usr/bin/env node

/**
 * Test script to debug archive flow
 */

require("ts-node/register");

async function testArchiveFlow() {
  try {
    console.log("🧪 Testing Archive Flow...\n");

    const {
      initializeDatabase,
      createDocument,
      getDocumentById,
      createArchive,
      getAllArchives,
      deleteDocument,
      getAllUsers,
      closeDatabase,
    } = require("../src/lib/database.ts");

    // Initialize database
    console.log("📦 Initializing database...");
    await initializeDatabase();
    console.log("✅ Database initialized\n");

    // Get users
    const users = await getAllUsers();
    if (users.length === 0) {
      console.log("❌ No users found. Please create a user first.");
      return;
    }
    const testUser = users[0];
    console.log(
      `✅ Using test user: ${testUser.username} (ID: ${testUser.id})\n`
    );

    // Create a test document
    console.log("📄 Creating test document...");
    const documentId = await createDocument(
      "Test Archive Document",
      "<PERSON>",
      Buffer.from("Test document data for archiving"),
      "approved",
      testUser.id
    );
    console.log(`✅ Test document created with ID: ${documentId}\n`);

    // Get the document to verify it exists
    console.log("🔍 Fetching created document...");
    const document = await getDocumentById(documentId);
    console.log(
      `✅ Document found: ${document?.document_name} - Status: ${document?.status}\n`
    );

    // Check archives before archiving
    console.log("📋 Checking archives before archiving...");
    const archivesBefore = await getAllArchives();
    console.log(`✅ Archives before: ${archivesBefore.length}\n`);

    // Create archive entry
    console.log("📦 Creating archive entry...");
    const archiveId = await createArchive(
      document.document_name,
      document.applicant_name,
      document.document_data,
      document.user_id,
      document.id,
      document.approved_at || undefined,
      undefined
    );
    console.log(`✅ Archive created with ID: ${archiveId}\n`);

    // Check archives after archiving
    console.log("📋 Checking archives after archiving...");
    const archivesAfter = await getAllArchives();
    console.log(`✅ Archives after: ${archivesAfter.length}`);
    if (archivesAfter.length > 0) {
      console.log(
        `   First archive: ${archivesAfter[0].document_name} by ${archivesAfter[0].applicant_name}\n`
      );
    }

    // Delete the original document (hard archive)
    console.log("🗑️ Deleting original document...");
    await deleteDocument(documentId);
    console.log("✅ Original document deleted\n");

    // Verify document is gone
    const deletedDocument = await getDocumentById(documentId);
    console.log(
      `✅ Document deletion verified: ${
        deletedDocument ? "Still exists" : "Successfully deleted"
      }\n`
    );

    console.log("🎉 Archive flow test completed successfully!");

    await closeDatabase();
  } catch (error) {
    console.error("❌ Error in archive flow test:", error);
    process.exit(1);
  }
}

testArchiveFlow();
