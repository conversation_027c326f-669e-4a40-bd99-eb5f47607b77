import { NextResponse } from 'next/server';
import { getAllArchives } from '@/lib/database';

/**
 * GET /api/archives - Get all archived documents
 */
export async function GET() {
  try {
    const archives = await getAllArchives();

    // Ensure uploaded_at is properly formatted for all archives
    const formattedArchives = archives.map(archive => ({
      ...archive,
      uploaded_at: archive.uploaded_at || new Date().toISOString()
    }));

    return NextResponse.json({
      archives: formattedArchives,
      count: formattedArchives.length
    });
  } catch (error) {
    console.error('Error fetching archives:', error);
    return NextResponse.json(
      { error: 'Failed to fetch archives' },
      { status: 500 }
    );
  }
}
