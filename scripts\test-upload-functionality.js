const fs = require("fs");
const path = require("path");
const FormData = require("form-data");
const { default: fetch } = require("node-fetch");

async function testPdfUpload() {
  console.log("🧪 Testing PDF Upload Functionality...\n");

  // Check if test PDF exists
  const testPdfPath = path.join(
    __dirname,
    "..",
    "temp",
    "test-ldis-document.pdf"
  );

  if (!fs.existsSync(testPdfPath)) {
    console.log("❌ Test PDF not found at:", testPdfPath);
    console.log("Please generate a test PDF first.");
    return;
  }

  console.log("📄 Test PDF found:", testPdfPath);
  const stats = fs.statSync(testPdfPath);
  console.log("📊 File size:", (stats.size / 1024).toFixed(2), "KB\n");

  try {
    // Read the PDF file
    const pdfBuffer = fs.readFileSync(testPdfPath);

    // Create form data
    const formData = new FormData();
    formData.append("pdfFile", pdfBuffer, {
      filename: "test-ldis-document.pdf",
      contentType: "application/pdf",
    });

    console.log("📡 Uploading PDF to server...");

    // Upload to the API
    const response = await fetch("http://localhost:3001/api/documents/upload", {
      method: "POST",
      body: formData,
      headers: formData.getHeaders(),
    });

    console.log("📊 Response status:", response.status);

    const result = await response.json();

    if (response.ok) {
      console.log("✅ Upload successful!");
      console.log("📋 Response:", JSON.stringify(result, null, 2));

      if (result.extractedData) {
        console.log("\n📝 Extracted Metadata:");
        console.log("   Document:", result.extractedData.document_name);
        console.log("   Applicant:", result.extractedData.applicant_name);
        console.log("   System:", result.extractedData.generation_info?.system);
        console.log(
          "   Fields:",
          Object.keys(result.extractedData.document_data || {}).length
        );
      }
    } else {
      console.log("❌ Upload failed!");
      console.log("📋 Error:", JSON.stringify(result, null, 2));
    }
  } catch (error) {
    console.error("❌ Error during upload test:", error.message);
    if (error.code === "ECONNREFUSED") {
      console.log(
        "💡 Make sure the development server is running on port 3001"
      );
      console.log("   Run: pnpm dev");
    }
  }
}

// Run the test
testPdfUpload().catch(console.error);
