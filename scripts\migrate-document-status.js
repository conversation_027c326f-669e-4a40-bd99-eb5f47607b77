#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to migrate document statuses from "uploaded" to "to review"
 * Run this script to update existing documents in the database
 */

const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Database path - adjust if needed
const dbPath = path.join(__dirname, '..', 'database.db');

console.log('🔄 Starting document status migration...');
console.log(`📁 Database path: ${dbPath}`);

// Open database connection
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ Error opening database:', err.message);
    process.exit(1);
  }
  console.log('✅ Connected to SQLite database');
});

// Run the migration
db.run(
  'UPDATE documents SET status = ? WHERE status = ?',
  ['to review', 'uploaded'],
  function(err) {
    if (err) {
      console.error('❌ Error during migration:', err.message);
      process.exit(1);
    }
    
    console.log(`✅ Migration completed successfully!`);
    console.log(`📊 Documents updated: ${this.changes}`);
    
    // Verify the migration
    db.get(
      'SELECT COUNT(*) as count FROM documents WHERE status = ?',
      ['to review'],
      (err, row) => {
        if (err) {
          console.error('❌ Error verifying migration:', err.message);
        } else {
          console.log(`📋 Total documents with "to review" status: ${row.count}`);
        }
        
        // Close database connection
        db.close((err) => {
          if (err) {
            console.error('❌ Error closing database:', err.message);
          } else {
            console.log('✅ Database connection closed');
            console.log('🎉 Migration completed successfully!');
          }
        });
      }
    );
  }
);
