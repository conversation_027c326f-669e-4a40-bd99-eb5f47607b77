import { NextRequest, NextResponse } from 'next/server';
import { 
  getArchiveById, 
  createDocument, 
  deleteArchive 
} from '@/lib/database';

/**
 * POST /api/archives/[id]/restore - Restore an archived document
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const archiveId = parseInt(id);
    
    if (isNaN(archiveId)) {
      return NextResponse.json(
        { error: 'Invalid archive ID' },
        { status: 400 }
      );
    }

    // Get archive from database first to verify it exists
    const archive = await getArchiveById(archiveId);
    
    if (!archive) {
      return NextResponse.json(
        { error: 'Archive not found' },
        { status: 404 }
      );
    }

    // Create new document from archive with "restored" status
    const documentId = await createDocument(
      archive.document_name,
      archive.applicant_name,
      archive.document_data,
      'restored', // Set status to "restored" as requested
      archive.user_id,
      false // not archived
    );

    // Delete the archive entry
    await deleteArchive(archiveId);

    return NextResponse.json(
      { 
        message: 'Document restored successfully',
        documentId: documentId
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Error restoring document:', error);
    return NextResponse.json(
      { error: 'Failed to restore document' },
      { status: 500 }
    );
  }
}
