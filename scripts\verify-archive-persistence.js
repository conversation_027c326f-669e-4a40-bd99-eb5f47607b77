#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to verify that archives persist after document deletion
 */

require("ts-node/register");

async function verifyArchivePersistence() {
  try {
    console.log("🧪 Verifying Archive Persistence...\n");

    const {
      initializeDatabase,
      createDocument,
      getDocumentById,
      createArchive,
      getAllArchives,
      deleteDocument,
      getAllUsers,
      closeDatabase,
    } = require("../src/lib/database.ts");

    // Initialize database
    console.log("📦 Initializing database...");
    await initializeDatabase();
    console.log("✅ Database initialized\n");

    // Get users
    const users = await getAllUsers();
    if (users.length === 0) {
      console.log("❌ No users found. Please create a user first.");
      return;
    }
    const testUser = users[0];
    console.log(`✅ Using test user: ${testUser.username} (ID: ${testUser.id})\n`);

    // Check initial archives count
    console.log("📋 Checking initial archives count...");
    const initialArchives = await getAllArchives();
    console.log(`✅ Initial archives count: ${initialArchives.length}\n`);

    // Create a test document
    console.log("📄 Creating test document...");
    const documentId = await createDocument(
      "Persistence Test Document",
      "Test User",
      Buffer.from("Test document data for persistence test"),
      "approved",
      testUser.id
    );
    console.log(`✅ Test document created with ID: ${documentId}\n`);

    // Get the document
    const document = await getDocumentById(documentId);
    console.log(`✅ Document verified: ${document?.document_name}\n`);

    // Create archive entry
    console.log("📦 Creating archive entry...");
    const archiveId = await createArchive(
      document.document_name,
      document.applicant_name,
      document.document_data,
      document.user_id,
      document.id,
      document.approved_at || undefined,
      undefined
    );
    console.log(`✅ Archive created with ID: ${archiveId}\n`);

    // Check archives count after creating archive
    console.log("📋 Checking archives count after creating archive...");
    const archivesAfterCreate = await getAllArchives();
    console.log(`✅ Archives after create: ${archivesAfterCreate.length}\n`);

    // Delete the original document
    console.log("🗑️ Deleting original document...");
    await deleteDocument(documentId);
    console.log("✅ Original document deleted\n");

    // CRITICAL TEST: Check archives count after deleting document
    console.log("🔍 CRITICAL TEST: Checking archives count after document deletion...");
    const archivesAfterDelete = await getAllArchives();
    console.log(`✅ Archives after document deletion: ${archivesAfterDelete.length}\n`);

    // Verify the archive still exists
    if (archivesAfterDelete.length === archivesAfterCreate.length) {
      console.log("🎉 SUCCESS: Archive persisted after document deletion!");
      console.log(`   Archive details: ${archivesAfterDelete[archivesAfterDelete.length - 1]?.document_name} by ${archivesAfterDelete[archivesAfterDelete.length - 1]?.applicant_name}`);
    } else {
      console.log("❌ FAILURE: Archive was deleted when document was deleted!");
      console.log(`   Expected: ${archivesAfterCreate.length}, Got: ${archivesAfterDelete.length}`);
    }

    console.log("\n📊 Summary:");
    console.log(`   Initial archives: ${initialArchives.length}`);
    console.log(`   After creating archive: ${archivesAfterCreate.length}`);
    console.log(`   After deleting document: ${archivesAfterDelete.length}`);
    console.log(`   Archive persistence: ${archivesAfterDelete.length === archivesAfterCreate.length ? '✅ PASSED' : '❌ FAILED'}`);

    await closeDatabase();
  } catch (error) {
    console.error("❌ Error in archive persistence test:", error);
    process.exit(1);
  }
}

verifyArchivePersistence();
