"use client";

import { useState } from "react";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { AuthDialog } from "@/components/auth-dialog";
import { useAdminAuth } from "@/hooks/use-local-storage";
import { LogOut, Clock } from "lucide-react";

export default function SettingsPage() {
  const {
    adminMode,
    isAuthenticated,
    authenticate,
    logout,
    toggleAdminMode,
    isAuthExpired,
    isLoading,
  } = useAdminAuth();
  const [showAuthDialog, setShowAuthDialog] = useState(false);

  const handleAdminModeToggle = (checked: boolean) => {
    const success = toggleAdminMode(checked);
    if (!success) {
      // Authentication is required
      setShowAuthDialog(true);
    }
  };

  const handleAuthSuccess = () => {
    authenticate();
    setShowAuthDialog(false);
  };

  const handleAuthCancel = () => {
    setShowAuthDialog(false);
  };

  const handleLogout = () => {
    logout();
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
          <p className="text-muted-foreground">Loading settings...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
        <p className="text-muted-foreground">
          Manage your application settings and preferences.
        </p>
      </div>

      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Admin Mode</CardTitle>
            <CardDescription>
              Enable admin mode to access administrative features and controls.
              Authentication is required to enable this mode.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Switch
                  id="admin-mode"
                  checked={adminMode}
                  onCheckedChange={handleAdminModeToggle}
                />
                <Label htmlFor="admin-mode">
                  {adminMode ? "Admin mode enabled" : "Enable admin mode"}
                </Label>
              </div>
              {isAuthenticated && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleLogout}
                  className="flex items-center gap-2"
                >
                  <LogOut className="h-4 w-4" />
                  Logout
                </Button>
              )}
            </div>

            {adminMode && (
              <div className="mt-4 p-4 bg-primary/10 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-primary font-medium">
                      Admin mode is active
                    </p>
                    <p className="text-sm text-muted-foreground mt-1">
                      You now have access to administrative features.
                    </p>
                  </div>
                  {isAuthExpired && (
                    <div className="flex items-center gap-1 text-amber-600">
                      <Clock className="h-4 w-4" />
                      <span className="text-xs">Session expires soon</span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {isAuthenticated && !adminMode && (
              <div className="mt-4 p-3 bg-muted rounded-lg">
                <p className="text-sm text-muted-foreground">
                  You are authenticated but admin mode is disabled. Toggle the
                  switch above to enable admin features.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <AuthDialog
        open={showAuthDialog}
        onClose={handleAuthCancel}
        onSuccess={handleAuthSuccess}
      />
    </>
  );
}
