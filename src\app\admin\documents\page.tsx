"use client";

import { useState, useEffect, useMemo } from "react";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  FileText,
  AlertCircle,
  RefreshCw,
  FolderOpen,
  Search,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Filter,
  X,
  Trash2,
  Archive,
  Loader2,
} from "lucide-react";
import { toast } from "sonner";
import { DeleteDocumentDialog } from "@/components/delete-document-dialog";
import { useMarkDocumentNotificationsAsRead } from "@/hooks/use-notifications";

interface Document {
  id: number;
  document_name: string;
  applicant_name: string;
  uploaded_at: string;
  document_data: Buffer | null;
  status: string;
  approved_at: string | null;
  user_id: number;
}

interface DocumentsResponse {
  documents: Document[];
  count: number;
}

type SortField = "document_name" | "applicant_name" | "uploaded_at" | "status";
type SortDirection = "asc" | "desc";

interface SortConfig {
  field: SortField;
  direction: SortDirection;
}

export default function DocumentsPage() {
  const router = useRouter();
  const [documents, setDocuments] = useState<Document[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Notification hook
  const markDocumentNotificationsAsRead = useMarkDocumentNotificationsAsRead();

  // Filtering and sorting state
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [sortConfig, setSortConfig] = useState<SortConfig>({
    field: "uploaded_at",
    direction: "desc",
  });
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState<Document | null>(
    null
  );
  const [selectedDocuments, setSelectedDocuments] = useState<Set<number>>(
    new Set()
  );
  const [archivingDocuments, setArchivingDocuments] = useState<Set<number>>(
    new Set()
  );

  const fetchDocuments = async () => {
    try {
      const response = await fetch("/api/documents");
      if (!response.ok) {
        throw new Error("Failed to fetch documents");
      }
      const data: DocumentsResponse = await response.json();
      setDocuments(data.documents);
      setError(null);
    } catch (err) {
      console.error("Error fetching documents:", err);
      setError(err instanceof Error ? err.message : "Unknown error");
      toast.error("Failed to load documents");
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchDocuments();
    setRefreshing(false);
    toast.success("Documents refreshed");
  };

  // Filtered and sorted documents
  const filteredAndSortedDocuments = useMemo(() => {
    let filtered = documents;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(
        (doc) =>
          doc.document_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          doc.applicant_name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter((doc) => {
        if (statusFilter === "to_review") {
          return (
            doc.status === "pending" ||
            doc.status === "uploaded" ||
            doc.status === "to review"
          );
        }
        return doc.status === statusFilter;
      });
    }

    // Apply sorting
    const sorted = [...filtered].sort((a, b) => {
      let aValue: string | number;
      let bValue: string | number;

      switch (sortConfig.field) {
        case "uploaded_at":
          aValue = new Date(a.uploaded_at).getTime();
          bValue = new Date(b.uploaded_at).getTime();
          break;
        case "document_name":
          aValue = a.document_name.toLowerCase();
          bValue = b.document_name.toLowerCase();
          break;
        case "applicant_name":
          aValue = a.applicant_name.toLowerCase();
          bValue = b.applicant_name.toLowerCase();
          break;
        case "status":
          aValue = a.status.toLowerCase();
          bValue = b.status.toLowerCase();
          break;
        default:
          return 0;
      }

      if (aValue < bValue) {
        return sortConfig.direction === "asc" ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortConfig.direction === "asc" ? 1 : -1;
      }
      return 0;
    });

    return sorted;
  }, [documents, searchTerm, statusFilter, sortConfig]);

  const handleSort = (field: SortField) => {
    setSortConfig((prev) => ({
      field,
      direction:
        prev.field === field && prev.direction === "asc" ? "desc" : "asc",
    }));
  };

  const handleClearFilters = () => {
    setSearchTerm("");
    setStatusFilter("all");
    setSortConfig({ field: "uploaded_at", direction: "desc" });
    toast.success("Filters cleared");
  };

  const handleDocumentClick = (document: Document) => {
    // Mark any unread notifications for this document as read
    markDocumentNotificationsAsRead.mutate(document.id);

    // Navigate to document details
    router.push(`/admin/documents/${document.id}`);
  };

  const handleDeleteDocument = (
    document: Document,
    event: React.MouseEvent
  ) => {
    // Prevent row click when delete button is clicked
    event.stopPropagation();

    // Open the delete dialog
    setDocumentToDelete(document);
    setDeleteDialogOpen(true);
  };

  const handleDocumentDeleted = async (documentId: number) => {
    // Refresh the documents list after deletion
    await fetchDocuments();
    // Remove from selected documents if it was selected
    setSelectedDocuments((prev) => {
      const newSet = new Set(prev);
      newSet.delete(documentId);
      return newSet;
    });
  };

  const handleArchiveDocument = async (
    document: Document,
    event: React.MouseEvent
  ) => {
    // Prevent row click when archive button is clicked
    event.stopPropagation();

    if (document.status !== "approved") {
      toast.error("Only approved documents can be archived");
      return;
    }

    setArchivingDocuments((prev) => new Set(prev).add(document.id));

    try {
      const response = await fetch(`/api/documents/${document.id}/archive`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error("Failed to archive document");
      }

      toast.success("Document archived successfully");
      await fetchDocuments(); // Refresh the list
    } catch (error) {
      console.error("Error archiving document:", error);
      toast.error("Failed to archive document");
    } finally {
      setArchivingDocuments((prev) => {
        const newSet = new Set(prev);
        newSet.delete(document.id);
        return newSet;
      });
    }
  };

  const toggleSelectAll = () => {
    if (selectedDocuments.size === filteredAndSortedDocuments.length) {
      setSelectedDocuments(new Set());
    } else {
      setSelectedDocuments(
        new Set(filteredAndSortedDocuments.map((d) => d.id))
      );
    }
  };

  const toggleSelectDocument = (documentId: number) => {
    setSelectedDocuments((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(documentId)) {
        newSet.delete(documentId);
      } else {
        newSet.add(documentId);
      }
      return newSet;
    });
  };

  const handleBulkDelete = async () => {
    if (selectedDocuments.size === 0) {
      toast.error("No documents selected");
      return;
    }

    if (
      !confirm(
        `Are you sure you want to delete ${selectedDocuments.size} document(s)? This action cannot be undone.`
      )
    ) {
      return;
    }

    try {
      const deletePromises = Array.from(selectedDocuments).map(
        async (documentId) => {
          const response = await fetch(`/api/documents/${documentId}`, {
            method: "DELETE",
          });
          if (!response.ok) {
            throw new Error(`Failed to delete document ${documentId}`);
          }
          return documentId;
        }
      );

      const deletedIds = await Promise.all(deletePromises);

      toast.success(`Successfully deleted ${deletedIds.length} document(s)`);
      setSelectedDocuments(new Set());
      await fetchDocuments();
    } catch (error) {
      console.error("Error deleting documents:", error);
      toast.error("Failed to delete some documents");
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) {
      return "No date";
    }

    try {
      // Handle different possible date formats from SQLite
      let date: Date;

      // If it's already a valid date string, use it directly
      if (typeof dateString === "string") {
        // SQLite CURRENT_TIMESTAMP format: "YYYY-MM-DD HH:MM:SS"
        // Convert to ISO format if needed
        if (dateString.includes(" ") && !dateString.includes("T")) {
          // Convert "YYYY-MM-DD HH:MM:SS" to "YYYY-MM-DDTHH:MM:SS"
          const isoString = dateString.replace(" ", "T");
          date = new Date(isoString);
        } else {
          date = new Date(dateString);
        }
      } else {
        date = new Date(dateString);
      }

      // Check if the date is valid
      if (isNaN(date.getTime())) {
        console.warn("Invalid date string:", dateString);
        return "Invalid date";
      }

      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      });
    } catch (error) {
      console.error(
        "Error formatting date:",
        error,
        "Date string:",
        dateString
      );
      return "Error formatting date";
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { variant: "secondary" as const, label: "To Review" },
      uploaded: { variant: "secondary" as const, label: "To Review" },
      "to review": { variant: "secondary" as const, label: "To Review" },
      approved: { variant: "default" as const, label: "Approved" },
      rejected: { variant: "destructive" as const, label: "Rejected" },
      restored: { variant: "outline" as const, label: "Restored" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      variant: "secondary" as const,
      label: "To Review",
    };

    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getSortIcon = (field: SortField) => {
    if (sortConfig.field !== field) {
      return <ArrowUpDown className="h-4 w-4" />;
    }
    return sortConfig.direction === "asc" ? (
      <ArrowUp className="h-4 w-4" />
    ) : (
      <ArrowDown className="h-4 w-4" />
    );
  };

  useEffect(() => {
    const loadDocuments = async () => {
      setLoading(true);
      await fetchDocuments();
      setLoading(false);
    };

    loadDocuments();
  }, []);

  if (loading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <Skeleton className="h-6 w-48" />
                <Skeleton className="h-4 w-64 mt-2" />
              </div>
              <Skeleton className="h-9 w-20" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Document Name</TableHead>
                    <TableHead>Applicant Name</TableHead>
                    <TableHead>Uploaded At</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="w-[100px]">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {[...Array(5)].map((_, i) => (
                    <TableRow key={i}>
                      <TableCell>
                        <Skeleton className="h-4 w-32" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-24" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-28" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-6 w-16" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-8 w-8" />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Documents
            </CardTitle>
            <CardDescription>
              Manage and review submitted documents
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Error loading documents: {error}
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Documents
              </CardTitle>
              <CardDescription>
                Manage and review submitted documents. Showing{" "}
                {filteredAndSortedDocuments.length} of {documents.length}{" "}
                document{documents.length !== 1 ? "s" : ""}.
              </CardDescription>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={refreshing}
              className="flex items-center gap-2"
            >
              <RefreshCw
                className={`h-4 w-4 ${refreshing ? "animate-spin" : ""}`}
              />
              Refresh
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {documents.length === 0 ? (
            <div className="text-center py-12">
              <FolderOpen className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No documents found</h3>
              <p className="text-muted-foreground">
                No documents have been submitted yet.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Filter and Search Controls */}
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search documents or applicants..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <div className="flex gap-2">
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-[180px]">
                      <Filter className="h-4 w-4 mr-2" />
                      <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="to_review">To Review</SelectItem>
                      <SelectItem value="approved">Approved</SelectItem>
                      <SelectItem value="rejected">Rejected</SelectItem>
                    </SelectContent>
                  </Select>
                  {(searchTerm ||
                    statusFilter !== "all" ||
                    sortConfig.field !== "uploaded_at" ||
                    sortConfig.direction !== "desc") && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleClearFilters}
                      className="flex items-center gap-2"
                    >
                      <X className="h-4 w-4" />
                      Clear
                    </Button>
                  )}
                </div>
              </div>

              {/* Bulk Actions */}
              {selectedDocuments.size > 0 && (
                <div className="flex items-center gap-4 p-3 bg-muted rounded-lg">
                  <span className="text-sm font-medium">
                    {selectedDocuments.size} document
                    {selectedDocuments.size !== 1 ? "s" : ""} selected
                  </span>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={handleBulkDelete}
                    className="flex items-center gap-2"
                  >
                    <Trash2 className="h-4 w-4" />
                    Delete selected
                  </Button>
                </div>
              )}

              {/* Results Summary */}
              {filteredAndSortedDocuments.length === 0 &&
              (searchTerm || statusFilter !== "all") ? (
                <div className="text-center py-8">
                  <FolderOpen className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                  <p className="text-muted-foreground">
                    No documents match your current filters.
                  </p>
                </div>
              ) : (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[50px]">
                          <Checkbox
                            checked={
                              selectedDocuments.size ===
                                filteredAndSortedDocuments.length &&
                              filteredAndSortedDocuments.length > 0
                            }
                            onCheckedChange={toggleSelectAll}
                            aria-label="Select all documents"
                          />
                        </TableHead>
                        <TableHead>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-auto p-0 font-medium hover:bg-transparent"
                            onClick={() => handleSort("document_name")}
                          >
                            Document Name
                            {getSortIcon("document_name")}
                          </Button>
                        </TableHead>
                        <TableHead>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-auto p-0 font-medium hover:bg-transparent"
                            onClick={() => handleSort("applicant_name")}
                          >
                            Applicant Name
                            {getSortIcon("applicant_name")}
                          </Button>
                        </TableHead>
                        <TableHead>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-auto p-0 font-medium hover:bg-transparent"
                            onClick={() => handleSort("uploaded_at")}
                          >
                            Uploaded At
                            {getSortIcon("uploaded_at")}
                          </Button>
                        </TableHead>
                        <TableHead>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-auto p-0 font-medium hover:bg-transparent"
                            onClick={() => handleSort("status")}
                          >
                            Status
                            {getSortIcon("status")}
                          </Button>
                        </TableHead>
                        <TableHead className="w-[100px]">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredAndSortedDocuments.map((document) => (
                        <TableRow
                          key={document.id}
                          className="cursor-pointer hover:bg-muted/50"
                          onClick={() => handleDocumentClick(document)}
                        >
                          <TableCell>
                            <Checkbox
                              checked={selectedDocuments.has(document.id)}
                              onCheckedChange={() =>
                                toggleSelectDocument(document.id)
                              }
                              onClick={(e) => e.stopPropagation()}
                              aria-label={`Select document ${document.document_name}`}
                            />
                          </TableCell>
                          <TableCell className="font-medium">
                            {document.document_name}
                          </TableCell>
                          <TableCell>{document.applicant_name}</TableCell>
                          <TableCell>
                            {formatDate(document.uploaded_at)}
                          </TableCell>
                          <TableCell>
                            {getStatusBadge(document.status)}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) =>
                                  handleArchiveDocument(document, e)
                                }
                                disabled={
                                  document.status !== "approved" ||
                                  archivingDocuments.has(document.id)
                                }
                                className="h-8 w-8 p-0 text-orange-600 hover:text-orange-700 hover:bg-orange-50 disabled:opacity-50"
                                title="Archive document"
                              >
                                {archivingDocuments.has(document.id) ? (
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                ) : (
                                  <Archive className="h-4 w-4" />
                                )}
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) =>
                                  handleDeleteDocument(document, e)
                                }
                                className="h-8 w-8 p-0 text-destructive hover:text-destructive hover:bg-destructive/10"
                                title="Delete document"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Delete Document Dialog */}
      <DeleteDocumentDialog
        document={documentToDelete}
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onDocumentDeleted={handleDocumentDeleted}
      />
    </div>
  );
}
