import { NextRequest, NextResponse } from 'next/server';
import { markAllNotificationsAsRead } from '@/lib/database';

/**
 * POST /api/notifications/mark-all-read - Mark all notifications as read for a user
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId } = body;

    if (!userId || isNaN(parseInt(userId))) {
      return NextResponse.json(
        { error: 'Valid user ID is required' },
        { status: 400 }
      );
    }

    // Mark all notifications as read for the user
    await markAllNotificationsAsRead(parseInt(userId));

    return NextResponse.json(
      { 
        message: 'All notifications marked as read',
        userId: parseInt(userId)
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    return NextResponse.json(
      { error: 'Failed to mark all notifications as read' },
      { status: 500 }
    );
  }
}
