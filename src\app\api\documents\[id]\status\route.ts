import { NextRequest, NextResponse } from 'next/server';
import { updateDocumentStatus, getDocumentById } from '@/lib/database';

/**
 * PUT /api/documents/[id]/status - Update document status
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const documentId = parseInt(id);
    
    if (isNaN(documentId)) {
      return NextResponse.json(
        { error: 'Invalid document ID' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { status, approved_at } = body;

    if (!status) {
      return NextResponse.json(
        { error: 'Status is required' },
        { status: 400 }
      );
    }

    // Get document from database first to verify it exists
    const document = await getDocumentById(documentId);
    
    if (!document) {
      return NextResponse.json(
        { error: 'Document not found' },
        { status: 404 }
      );
    }

    // Update document status
    await updateDocumentStatus(documentId, status, approved_at);

    // Get updated document
    const updatedDocument = await getDocumentById(documentId);

    return NextResponse.json(
      { 
        message: 'Document status updated successfully',
        document: updatedDocument
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Error updating document status:', error);
    return NextResponse.json(
      { error: 'Failed to update document status' },
      { status: 500 }
    );
  }
}
