{"name": "ldis", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:init": "node scripts/init-db.js", "db:test": "node scripts/test-db.js", "templates:test": "node scripts/test-templates.js", "notifications:test": "node scripts/test-notifications-database.js", "auth:test": "node scripts/test-auth.js", "storage:test": "node scripts/test-localstorage.js", "admin:test": "node scripts/test-admin-protection.js", "ssr:test": "node scripts/test-ssr-fix.js"}, "dependencies": {"@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@radix-ui/react-visually-hidden": "^1.2.3", "@tanstack/react-query": "^5.83.1", "@tanstack/react-query-devtools": "^5.83.1", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.534.0", "next": "15.4.5", "next-themes": "^0.4.6", "pdf-parse": "^1.1.1", "react": "19.1.0", "react-dom": "19.1.0", "sonner": "^2.0.6", "sqlite3": "^5.1.7", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@shadcn/ui": "^0.0.4", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/pdf-parse": "^1.1.5", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "form-data": "^4.0.4", "node-fetch": "^3.3.2", "tailwindcss": "^4", "ts-node": "^10.9.2", "tw-animate-css": "^1.3.6", "typescript": "^5"}}