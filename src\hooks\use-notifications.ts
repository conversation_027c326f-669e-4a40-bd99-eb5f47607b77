import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'

export interface Notification {
  id: number
  document_id: number
  document_name: string
  applicant_name: string
  is_read: boolean
  uploaded_at: string
  user_id: number
}

export interface NotificationsResponse {
  notifications: Notification[]
  unreadCount: number
  count: number
}

// Query keys
export const notificationKeys = {
  all: ['notifications'] as const,
  lists: () => [...notificationKeys.all, 'list'] as const,
  list: (filters: string) => [...notificationKeys.lists(), { filters }] as const,
  details: () => [...notificationKeys.all, 'detail'] as const,
  detail: (id: number) => [...notificationKeys.details(), id] as const,
}

// Fetch all notifications
export function useNotifications() {
  return useQuery({
    queryKey: notificationKeys.lists(),
    queryFn: async (): Promise<NotificationsResponse> => {
      const response = await fetch('/api/notifications')
      if (!response.ok) {
        throw new Error('Failed to fetch notifications')
      }
      return response.json()
    },
    staleTime: 3 * 1000, // 3 seconds
    refetchInterval: 3 * 1000, // Refetch every 3 seconds for real-time updates
  })
}

// Mark notification as read
export function useMarkNotificationAsRead() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (notificationId: number) => {
      const response = await fetch(`/api/notifications/${notificationId}`, {
        method: 'PATCH',
      })
      if (!response.ok) {
        throw new Error('Failed to mark notification as read')
      }
      return response.json()
    },
    onSuccess: () => {
      // Invalidate and refetch notifications
      queryClient.invalidateQueries({ queryKey: notificationKeys.lists() })
      toast.success('Notification marked as read')
    },
    onError: (error) => {
      console.error('Error marking notification as read:', error)
      toast.error('Failed to mark notification as read')
    },
  })
}

// Delete notification
export function useDeleteNotification() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (notificationId: number) => {
      const response = await fetch(`/api/notifications/${notificationId}`, {
        method: 'DELETE',
      })
      if (!response.ok) {
        throw new Error('Failed to delete notification')
      }
      return response.json()
    },
    onSuccess: () => {
      // Invalidate and refetch notifications
      queryClient.invalidateQueries({ queryKey: notificationKeys.lists() })
      toast.success('Notification deleted')
    },
    onError: (error) => {
      console.error('Error deleting notification:', error)
      toast.error('Failed to delete notification')
    },
  })
}

// Mark all notifications as read
export function useMarkAllNotificationsAsRead() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (userId: number) => {
      const response = await fetch('/api/notifications/mark-all-read', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId }),
      })
      if (!response.ok) {
        throw new Error('Failed to mark all notifications as read')
      }
      return response.json()
    },
    onSuccess: () => {
      // Invalidate and refetch notifications
      queryClient.invalidateQueries({ queryKey: notificationKeys.lists() })
      toast.success('All notifications marked as read')
    },
    onError: (error) => {
      console.error('Error marking all notifications as read:', error)
      toast.error('Failed to mark all notifications as read')
    },
  })
}

// Mark document notifications as read
export function useMarkDocumentNotificationsAsRead() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (documentId: number) => {
      const response = await fetch('/api/notifications/mark-document-read', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ documentId }),
      })
      if (!response.ok) {
        throw new Error('Failed to mark document notifications as read')
      }
      return response.json()
    },
    onSuccess: () => {
      // Invalidate and refetch notifications
      queryClient.invalidateQueries({ queryKey: notificationKeys.lists() })
    },
    onError: (error) => {
      console.error('Error marking document notifications as read:', error)
    },
  })
}

// Bulk delete notifications
export function useBulkDeleteNotifications() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (notificationIds: number[]) => {
      const response = await fetch('/api/notifications/bulk-delete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ notificationIds }),
      })
      if (!response.ok) {
        throw new Error('Failed to delete notifications')
      }
      return response.json()
    },
    onSuccess: (data) => {
      // Invalidate and refetch notifications
      queryClient.invalidateQueries({ queryKey: notificationKeys.lists() })
      toast.success(`Successfully deleted ${data.totalDeleted} notification(s)`)
    },
    onError: (error) => {
      console.error('Error deleting notifications:', error)
      toast.error('Failed to delete notifications')
    },
  })
}

// Get current user (helper for mark all as read)
export function useCurrentUser() {
  return useQuery({
    queryKey: ['user', 'current'],
    queryFn: async () => {
      const response = await fetch('/api/auth/me')
      if (!response.ok) {
        throw new Error('Failed to get user information')
      }
      return response.json()
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}
