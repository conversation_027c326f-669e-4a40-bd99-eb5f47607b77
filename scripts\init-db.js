#!/usr/bin/env node

/**
 * Database initialization script for LDIS
 * This script initializes the SQLite database and creates the required tables
 */

const path = require("path");
const fs = require("fs");

// Add the src directory to the module path for TypeScript compilation
require("ts-node/register");

async function initializeDatabase() {
  try {
    console.log("🚀 Initializing LDIS SQLite Database...\n");

    // Import the database module
    const {
      initializeDatabase,
      getAllUsers,
      closeDatabase,
    } = require("../src/lib/database.ts");

    // Initialize the database
    console.log("📦 Setting up database connection...");
    await initializeDatabase();
    console.log("✅ Database connection established\n");

    // Verify tables were created
    console.log("🔍 Verifying database setup...");
    const users = await getAllUsers();
    console.log(`✅ Users table verified (${users.length} users found)\n`);

    // Show database file location
    const dbPath =
      process.env.DATABASE_PATH || path.join(process.cwd(), "data", "ldis.db");
    const dbExists = fs.existsSync(dbPath);
    console.log(`📁 Database file: ${dbPath}`);
    console.log(`📊 Database file exists: ${dbExists ? "✅ Yes" : "❌ No"}\n`);

    if (dbExists) {
      const stats = fs.statSync(dbPath);
      console.log(
        `📏 Database file size: ${(stats.size / 1024).toFixed(2)} KB`
      );
      console.log(`📅 Created: ${stats.birthtime.toLocaleString()}\n`);
    }

    console.log("🎉 Database initialization completed successfully!");
    console.log("\nNext steps:");
    console.log("1. The database is ready to use in your Next.js application");
    console.log("2. Import database functions from src/lib/database.ts");
    console.log(
      "3. Use the provided functions to manage users and authentication\n"
    );

    // Close the database connection
    await closeDatabase();
  } catch (error) {
    console.error("❌ Error initializing database:", error);
    process.exit(1);
  }
}

// Run the initialization
initializeDatabase();
