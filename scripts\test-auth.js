#!/usr/bin/env node

/**
 * Authentication API test script for LDIS
 * This script tests the authentication endpoints
 */

const path = require("path");

// Add the src directory to the module path for TypeScript compilation
require("ts-node/register");

async function testAuthAPI() {
  try {
    console.log("🧪 Testing LDIS Authentication API...\n");

    // Import the database module to ensure it's initialized
    const {
      initializeDatabase,
      getAllUsers,
      closeDatabase,
    } = require("../src/lib/database.ts");

    // Initialize the database
    console.log("📦 Initializing database...");
    await initializeDatabase();
    console.log("✅ Database initialized\n");

    // Check initial user count
    const initialUsers = await getAllUsers();
    console.log(`👥 Initial user count: ${initialUsers.length}\n`);

    // Test data
    const testUser = {
      username: "admin",
      password: "admin123",
      recoveryKey: "", // Empty to test auto-generation
    };

    console.log("🔐 Testing authentication flow...\n");

    // Test 1: Sign up with auto-generated recovery key (should succeed)
    console.log("1️⃣ Testing sign up with auto-generated recovery key...");
    let generatedRecoveryKey = "";
    try {
      // Simulate the auto-generation logic
      const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
      let result = "";
      for (let i = 0; i < 12; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
      }
      generatedRecoveryKey = result;

      const bcrypt = require("bcryptjs");
      const {
        createUser,
        getUserByUsername,
      } = require("../src/lib/database.ts");

      const hashedPassword = await bcrypt.hash(testUser.password, 12);
      const userId = await createUser(
        testUser.username,
        hashedPassword,
        generatedRecoveryKey
      );
      console.log(`✅ Sign up successful - User ID: ${userId}`);
      console.log(`🔑 Auto-generated recovery key: ${generatedRecoveryKey}`);
    } catch (error) {
      console.log(`❌ Sign up failed: ${error.message}`);
    }

    // Test 2: Sign in (should succeed)
    console.log("\n2️⃣ Testing sign in...");
    try {
      const bcrypt = require("bcryptjs");
      const { getUserByUsername } = require("../src/lib/database.ts");

      const user = await getUserByUsername(testUser.username);
      if (user) {
        const passwordMatch = await bcrypt.compare(
          testUser.password,
          user.password
        );
        if (passwordMatch) {
          console.log("✅ Sign in successful");
        } else {
          console.log("❌ Sign in failed - Invalid password");
        }
      } else {
        console.log("❌ Sign in failed - User not found");
      }
    } catch (error) {
      console.log(`❌ Sign in failed: ${error.message}`);
    }

    // Test 3: Password reset (should succeed)
    console.log("\n3️⃣ Testing password reset...");
    try {
      const bcrypt = require("bcryptjs");
      const {
        getUserByUsername,
        updateUserPassword,
      } = require("../src/lib/database.ts");

      const user = await getUserByUsername(testUser.username);
      if (user && user.recovery_key === generatedRecoveryKey) {
        const newPassword = "newpassword123";
        const hashedNewPassword = await bcrypt.hash(newPassword, 12);
        await updateUserPassword(user.id, hashedNewPassword);

        // Verify the password was updated
        const updatedUser = await getUserByUsername(testUser.username);
        const passwordMatch = await bcrypt.compare(
          newPassword,
          updatedUser.password
        );

        if (passwordMatch) {
          console.log("✅ Password reset successful");
        } else {
          console.log("❌ Password reset failed - Password not updated");
        }
      } else {
        console.log("❌ Password reset failed - Invalid recovery key");
      }
    } catch (error) {
      console.log(`❌ Password reset failed: ${error.message}`);
    }

    // Test 4: Attempt second user signup (should fail)
    console.log("\n4️⃣ Testing second user signup (should fail)...");
    try {
      const { getAllUsers } = require("../src/lib/database.ts");
      const users = await getAllUsers();

      if (users.length > 0) {
        console.log(
          "✅ Second signup correctly blocked - Only one user allowed"
        );
      } else {
        console.log("❌ No users found - unexpected state");
      }
    } catch (error) {
      console.log(`❌ Test failed: ${error.message}`);
    }

    // Final user count
    const finalUsers = await getAllUsers();
    console.log(`\n👥 Final user count: ${finalUsers.length}`);

    if (finalUsers.length > 0) {
      console.log("\n📋 User details:");
      finalUsers.forEach((user, index) => {
        console.log(
          `${index + 1}. ${user.username} (ID: ${user.id}) - Created: ${
            user.created_at
          }`
        );
      });
    }

    console.log("\n🎉 Authentication API tests completed!");

    // Close the database connection
    await closeDatabase();
  } catch (error) {
    console.error("❌ Authentication test failed:", error);
    process.exit(1);
  }
}

// Run the test
testAuthAPI();
