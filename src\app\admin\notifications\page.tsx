"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Bell,
  AlertCircle,
  RefreshCw,
  Trash2,
  CheckCheck,
  FileText,
} from "lucide-react";
import { toast } from "sonner";
import {
  useNotifications,
  useMarkNotificationAsRead,
  useDeleteNotification,
  useMarkAllNotificationsAsRead,
  useBulkDeleteNotifications,
  useCurrentUser,
  type Notification,
} from "@/hooks/use-notifications";

export default function NotificationsPage() {
  const router = useRouter();
  const [selectedNotifications, setSelectedNotifications] = useState<
    Set<number>
  >(new Set());

  // TanStack Query hooks
  const {
    data: notificationsData,
    isLoading,
    error,
    refetch,
  } = useNotifications();
  const { data: currentUser } = useCurrentUser();
  const markAsReadMutation = useMarkNotificationAsRead();
  const deleteNotificationMutation = useDeleteNotification();
  const markAllAsReadMutation = useMarkAllNotificationsAsRead();
  const bulkDeleteMutation = useBulkDeleteNotifications();

  // Extract data from query response
  const notifications = notificationsData?.notifications || [];
  const unreadCount = notificationsData?.unreadCount || 0;

  // Mutation handlers
  const handleRefresh = () => {
    refetch();
  };

  const handleNotificationClick = (notification: Notification) => {
    // Mark as read when notification is clicked
    if (!notification.is_read) {
      markAsReadMutation.mutate(notification.id);
    }

    // Navigate to the document details page
    router.push(`/admin/documents/${notification.document_id}`);
  };

  const handleDeleteNotification = (notificationId: number) => {
    deleteNotificationMutation.mutate(notificationId, {
      onSuccess: () => {
        // Remove from selected notifications
        setSelectedNotifications((prev) => {
          const newSet = new Set(prev);
          newSet.delete(notificationId);
          return newSet;
        });
      },
    });
  };

  const handleMarkAllAsRead = () => {
    if (currentUser?.id) {
      markAllAsReadMutation.mutate(currentUser.id);
    }
  };

  const handleBulkDelete = () => {
    if (selectedNotifications.size === 0) {
      toast.error("No notifications selected");
      return;
    }

    if (
      !confirm(
        `Are you sure you want to delete ${selectedNotifications.size} notification(s)? This action cannot be undone.`
      )
    ) {
      return;
    }

    bulkDeleteMutation.mutate(Array.from(selectedNotifications), {
      onSuccess: () => {
        setSelectedNotifications(new Set());
      },
    });
  };

  const toggleSelectAll = () => {
    if (selectedNotifications.size === notifications.length) {
      setSelectedNotifications(new Set());
    } else {
      setSelectedNotifications(new Set(notifications.map((n) => n.id)));
    }
  };

  const toggleSelectNotification = (notificationId: number) => {
    setSelectedNotifications((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(notificationId)) {
        newSet.delete(notificationId);
      } else {
        newSet.add(notificationId);
      }
      return newSet;
    });
  };

  const formatDate = (dateString: string) => {
    if (!dateString) {
      return "No date";
    }
    try {
      return new Date(dateString).toLocaleString();
    } catch (error) {
      return "Invalid date";
    }
  };

  const getStatusBadge = (isRead: boolean) => {
    return (
      <Badge variant={isRead ? "secondary" : "default"}>
        {isRead ? "Read" : "Unread"}
      </Badge>
    );
  };

  // Loading state from any active mutations
  const isAnyMutationLoading =
    markAsReadMutation.isPending ||
    deleteNotificationMutation.isPending ||
    markAllAsReadMutation.isPending ||
    bulkDeleteMutation.isPending;

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <Skeleton className="h-6 w-48" />
                <Skeleton className="h-4 w-64 mt-2" />
              </div>
              <Skeleton className="h-9 w-20" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[50px]"></TableHead>
                    <TableHead>Document Name</TableHead>
                    <TableHead>Applicant Name</TableHead>
                    <TableHead>Uploaded At</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="w-[150px]">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {[...Array(5)].map((_, i) => (
                    <TableRow key={i}>
                      <TableCell>
                        <Skeleton className="h-4 w-4" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-32" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-24" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-28" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-6 w-16" />
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Skeleton className="h-8 w-8" />
                          <Skeleton className="h-8 w-8" />
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error.message}</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                Notifications
              </CardTitle>
              <CardDescription>
                Manage document upload notifications. Showing{" "}
                {notifications.length} notification
                {notifications.length !== 1 ? "s" : ""}.
                {unreadCount > 0 && (
                  <span className="ml-2 text-blue-600 font-medium">
                    {unreadCount} unread
                  </span>
                )}
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              {unreadCount > 0 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleMarkAllAsRead}
                  disabled={isAnyMutationLoading || !currentUser}
                  className="flex items-center gap-2"
                >
                  <CheckCheck className="h-4 w-4" />
                  Mark all read
                </Button>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={isLoading}
                className="flex items-center gap-2"
              >
                <RefreshCw
                  className={`h-4 w-4 ${isLoading ? "animate-spin" : ""}`}
                />
                Refresh
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {notifications.length === 0 ? (
            <div className="text-center py-12">
              <Bell className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No notifications</h3>
              <p className="text-muted-foreground">
                You'll see notifications here when documents are uploaded.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Bulk Actions */}
              {selectedNotifications.size > 0 && (
                <div className="flex items-center gap-4 p-3 bg-muted rounded-lg">
                  <span className="text-sm font-medium">
                    {selectedNotifications.size} notification
                    {selectedNotifications.size !== 1 ? "s" : ""} selected
                  </span>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={handleBulkDelete}
                    disabled={isAnyMutationLoading}
                    className="flex items-center gap-2"
                  >
                    <Trash2 className="h-4 w-4" />
                    Delete selected
                  </Button>
                </div>
              )}

              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[50px]">
                        <Checkbox
                          checked={
                            selectedNotifications.size ===
                              notifications.length && notifications.length > 0
                          }
                          onCheckedChange={toggleSelectAll}
                          aria-label="Select all notifications"
                        />
                      </TableHead>
                      <TableHead>Document Name</TableHead>
                      <TableHead>Applicant Name</TableHead>
                      <TableHead>Uploaded At</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="w-[150px]">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {notifications.map((notification) => (
                      <TableRow
                        key={notification.id}
                        className={`cursor-pointer hover:bg-muted/50 ${
                          !notification.is_read
                            ? "bg-blue-50/50 dark:bg-blue-950/20"
                            : ""
                        }`}
                        onClick={() => handleNotificationClick(notification)}
                      >
                        <TableCell>
                          <Checkbox
                            checked={selectedNotifications.has(notification.id)}
                            onCheckedChange={() =>
                              toggleSelectNotification(notification.id)
                            }
                            onClick={(e) => e.stopPropagation()}
                            aria-label={`Select notification for ${notification.document_name}`}
                          />
                        </TableCell>
                        <TableCell className="font-medium">
                          <div className="flex items-center gap-2">
                            <FileText className="h-4 w-4 text-muted-foreground" />
                            {notification.document_name}
                            {!notification.is_read && (
                              <div className="h-2 w-2 bg-blue-500 rounded-full" />
                            )}
                          </div>
                        </TableCell>
                        <TableCell>{notification.applicant_name}</TableCell>
                        <TableCell>
                          {formatDate(notification.uploaded_at)}
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(notification.is_read)}
                        </TableCell>
                        <TableCell>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteNotification(notification.id);
                            }}
                            disabled={isAnyMutationLoading}
                            className="h-8 w-8 p-0 text-destructive hover:text-destructive hover:bg-destructive/10"
                            title="Delete notification"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
