"use client";

import { usePathname } from "next/navigation";
import Link from "next/link";
import { Home } from "lucide-react";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

// Define route mappings for better display names
const routeMap: Record<string, string> = {
  "": "Home",
  settings: "Settings",
  admin: "Administration",
  dashboard: "Dashboard",
  documents: "Documents",
  templates: "Templates",
  template: "Template Preview",
  notifications: "Notifications",
  archives: "Archives",
  upload: "Upload Document",
  qr: "QR Access",
  manage: "Manage",
  add: "Add",
  users: "Users",
  security: "Security",
  config: "Configuration",
};

// Define which routes should not be clickable (current page)
const isCurrentPage = (segments: string[], index: number): boolean => {
  return index === segments.length - 1;
};

// Define the breadcrumb item type
interface BreadcrumbItem {
  href: string;
  label: string;
  isHome: boolean;
  isCurrent?: boolean;
}

// Generate breadcrumb items from pathname
const generateBreadcrumbItems = (pathname: string): BreadcrumbItem[] => {
  const segments = pathname.split("/").filter(Boolean);

  // Always start with Home
  const items: BreadcrumbItem[] = [
    {
      href: "/",
      label: "Home",
      isHome: true,
    },
  ];

  // Add segments
  let currentPath = "";
  segments.forEach((segment, index) => {
    currentPath += `/${segment}`;
    let label =
      routeMap[segment] || segment.charAt(0).toUpperCase() + segment.slice(1);

    // Handle dynamic routes like document IDs
    if (/^\d+$/.test(segment)) {
      // If it's a number and the previous segment is 'documents', show as "Document Details"
      if (index > 0 && segments[index - 1] === "documents") {
        label = "Document Details";
      } else {
        label = `#${segment}`;
      }
    }

    items.push({
      href: currentPath,
      label,
      isHome: false,
      isCurrent: isCurrentPage(segments, index),
    });
  });

  return items;
};

export default function DynamicBreadcrumb() {
  const pathname = usePathname();

  // Don't show breadcrumb on home page
  if (pathname === "/") {
    return null;
  }

  const breadcrumbItems = generateBreadcrumbItems(pathname);

  return (
    <div className="pb-4">
      <Breadcrumb>
        <BreadcrumbList className="text-sm">
          {breadcrumbItems.map((item, index) => (
            <div key={item.href} className="flex items-center">
              <BreadcrumbItem>
                {item.isCurrent ? (
                  <BreadcrumbPage className="flex items-center gap-1.5 font-medium text-foreground">
                    {item.isHome && <Home className="h-4 w-4" />}
                    {item.label}
                  </BreadcrumbPage>
                ) : (
                  <BreadcrumbLink asChild>
                    <Link
                      href={item.href}
                      className="flex items-center gap-1.5 text-muted-foreground hover:text-primary transition-colors"
                    >
                      {item.isHome && <Home className="h-4 w-4" />}
                      {item.label}
                    </Link>
                  </BreadcrumbLink>
                )}
              </BreadcrumbItem>
              {index < breadcrumbItems.length - 1 && (
                <BreadcrumbSeparator className="text-muted-foreground/50" />
              )}
            </div>
          ))}
        </BreadcrumbList>
      </Breadcrumb>
    </div>
  );
}
