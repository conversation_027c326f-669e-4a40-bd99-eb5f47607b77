"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, AlertCircle, Save } from "lucide-react";
import { toast } from "sonner";

interface Template {
  id: number;
  template_name: string;
  description?: string;
  filename: string;
  placeholders: string[];
  layout_size?: string;
  uploaded_at: string;
  user_id: number;
  username: string;
}

interface EditTemplateDialogProps {
  template: Template | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onTemplateUpdated: (updatedTemplate: Template) => void;
}

const LAYOUT_SIZES = [
  { value: "A4", label: "A4 (210 × 297 mm)" },
  { value: "Letter", label: "Letter (8.5 × 11 in)" },
];

export function EditTemplateDialog({
  template,
  open,
  onOpenChange,
  onTemplateUpdated,
}: EditTemplateDialogProps) {
  const [description, setDescription] = useState("");
  const [layoutSize, setLayoutSize] = useState("A4");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  // Reset form when template changes or dialog opens
  useEffect(() => {
    if (template && open) {
      setDescription(template.description || "");
      setLayoutSize(template.layout_size || "A4");
      setError("");
    }
  }, [template, open]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!template) return;

    setLoading(true);
    setError("");

    try {
      const response = await fetch(`/api/templates/${template.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          description: description.trim() || undefined,
          layout_size: layoutSize,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update template");
      }

      const data = await response.json();

      // Update the template in the parent component
      onTemplateUpdated(data.template);

      // Close the dialog
      onOpenChange(false);

      toast.success("Template updated successfully");
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to update template";
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Handle dialog close
  const handleClose = () => {
    if (!loading) {
      onOpenChange(false);
      setError("");
    }
  };

  if (!template) return null;

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Save className="h-5 w-5" />
            Edit Template
          </DialogTitle>
          <DialogDescription>
            Update the description and layout size for &quot;
            {template.template_name}
            &quot;. Template name and placeholders cannot be modified.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Template Name (Read-only) */}
          <div className="space-y-2">
            <Label htmlFor="templateName">Template Name</Label>
            <Input
              id="templateName"
              value={template.template_name}
              disabled
              className="bg-muted"
            />
            <p className="text-xs text-muted-foreground">
              Template name cannot be changed
            </p>
          </div>

          {/* Filename (Read-only) */}
          <div className="space-y-2">
            <Label htmlFor="filename">Filename</Label>
            <Input
              id="filename"
              value={template.filename}
              disabled
              className="bg-muted font-mono text-sm"
            />
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              placeholder="Enter a description for this template..."
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              disabled={loading}
              rows={3}
              className="resize-none"
            />
            <p className="text-xs text-muted-foreground">
              Optional description to help identify this template
            </p>
          </div>

          {/* Layout Size */}
          <div className="space-y-2">
            <Label htmlFor="layoutSize">Layout Size</Label>
            <Select
              value={layoutSize}
              onValueChange={setLayoutSize}
              disabled={loading}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select layout size" />
              </SelectTrigger>
              <SelectContent>
                {LAYOUT_SIZES.map((size) => (
                  <SelectItem key={size.value} value={size.value}>
                    {size.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground">
              Page size for document generation
            </p>
          </div>

          {/* Placeholders (Read-only) */}
          <div className="space-y-2">
            <Label>Placeholders</Label>
            <div className="p-3 bg-muted rounded-md">
              {template.placeholders.length > 0 ? (
                <div className="flex flex-wrap gap-1">
                  {template.placeholders.map((placeholder, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-2 py-1 rounded-md bg-background text-xs font-mono"
                    >
                      [{placeholder}]
                    </span>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground italic">
                  No placeholders found in this template
                </p>
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              Placeholders are automatically detected from the template file
            </p>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Save Changes
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
