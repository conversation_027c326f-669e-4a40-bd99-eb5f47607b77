#!/usr/bin/env node

/**
 * Templates table test script for LDIS
 * This script tests the templates table functionality
 */

require("ts-node/register");

async function testTemplates() {
  try {
    console.log("🧪 Testing LDIS Templates Table...\n");

    // Import the database module
    const {
      initializeDatabase,
      getAllUsers,
      createTemplate,
      getTemplateById,
      getTemplatesByUserId,
      getAllTemplates,
      updateTemplate,
      deleteTemplate,
      getTemplatesWithUserInfo,
      closeDatabase,
    } = require("../src/lib/database.ts");

    // Initialize the database
    console.log("📦 Initializing database...");
    await initializeDatabase();
    console.log("✅ Database initialized\n");

    // Get existing users to test with
    console.log("👥 Getting existing users...");
    const users = await getAllUsers();
    console.log(`✅ Found ${users.length} users\n`);

    if (users.length === 0) {
      console.log("❌ No users found. Please create a user first using the auth test script.");
      await closeDatabase();
      return;
    }

    const testUserId = users[0].id;
    console.log(`🎯 Using user ID ${testUserId} for testing\n`);

    // Test creating a template
    console.log("📄 Creating test template...");
    const templateId = await createTemplate(
      "Test Contract Template",
      "A test template for legal contracts",
      "test_contract.docx",
      JSON.stringify(["client_name", "date", "amount", "service_description"]),
      "A4",
      testUserId
    );
    console.log(`✅ Template created with ID: ${templateId}\n`);

    // Test getting template by ID
    console.log("🔍 Getting template by ID...");
    const template = await getTemplateById(templateId);
    console.log("✅ Template retrieved:");
    console.log(`   - Name: ${template?.template_name}`);
    console.log(`   - Description: ${template?.description}`);
    console.log(`   - Filename: ${template?.filename}`);
    console.log(`   - Placeholders: ${template?.placeholders}`);
    console.log(`   - Layout Size: ${template?.layout_size}`);
    console.log(`   - User ID: ${template?.user_id}\n`);

    // Test getting templates by user ID
    console.log("👤 Getting templates for user...");
    const userTemplates = await getTemplatesByUserId(testUserId);
    console.log(`✅ Found ${userTemplates.length} templates for user ${testUserId}\n`);

    // Test getting all templates
    console.log("📋 Getting all templates...");
    const allTemplates = await getAllTemplates();
    console.log(`✅ Found ${allTemplates.length} total templates\n`);

    // Test updating template
    console.log("✏️  Updating template...");
    await updateTemplate(
      templateId,
      "Updated Contract Template",
      "An updated test template for legal contracts",
      JSON.stringify(["client_name", "date", "amount", "service_description", "terms"]),
      "Letter"
    );
    console.log("✅ Template updated\n");

    // Verify update
    console.log("🔍 Verifying update...");
    const updatedTemplate = await getTemplateById(templateId);
    console.log("✅ Updated template:");
    console.log(`   - Name: ${updatedTemplate?.template_name}`);
    console.log(`   - Description: ${updatedTemplate?.description}`);
    console.log(`   - Layout Size: ${updatedTemplate?.layout_size}\n`);

    // Test getting templates with user info
    console.log("🔗 Getting templates with user info...");
    const templatesWithUsers = await getTemplatesWithUserInfo();
    console.log(`✅ Found ${templatesWithUsers.length} templates with user info:`);
    templatesWithUsers.forEach((t, index) => {
      console.log(`   ${index + 1}. ${t.template_name} (by ${t.username})`);
    });
    console.log();

    // Test deleting template
    console.log("🗑️  Deleting test template...");
    await deleteTemplate(templateId);
    console.log("✅ Template deleted\n");

    // Verify deletion
    console.log("🔍 Verifying deletion...");
    const deletedTemplate = await getTemplateById(templateId);
    console.log(`✅ Template deletion verified: ${deletedTemplate ? 'Still exists (ERROR)' : 'Successfully deleted'}\n`);

    console.log("🎉 Templates table test completed successfully!");
    console.log("\nTemplates table features tested:");
    console.log("✅ Create template");
    console.log("✅ Get template by ID");
    console.log("✅ Get templates by user ID");
    console.log("✅ Get all templates");
    console.log("✅ Update template");
    console.log("✅ Delete template");
    console.log("✅ Get templates with user info (JOIN)");
    console.log("✅ Foreign key constraint (user_id -> users.id)");

    // Close the database connection
    await closeDatabase();
  } catch (error) {
    console.error("❌ Error testing templates:", error);
    process.exit(1);
  }
}

// Run the test
testTemplates();
