import { NextRequest, NextResponse } from 'next/server';
import { createTemplate } from '@/lib/database';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';

/**
 * Extract placeholders from HTML content
 * Looks for patterns like [PLACEHOLDER_NAME]
 */
function extractPlaceholders(htmlContent: string): string[] {
  const placeholderRegex = /\[([A-Z_\s]+)\]/g;
  const placeholders: string[] = [];
  let match;
  
  while ((match = placeholderRegex.exec(htmlContent)) !== null) {
    const placeholder = match[1].trim();
    if (!placeholders.includes(placeholder)) {
      placeholders.push(placeholder);
    }
  }
  
  return placeholders;
}

/**
 * Check if HTML content contains image references
 */
function hasImageReferences(htmlContent: string): boolean {
  const imgRegex = /<img[^>]+src\s*=\s*["']([^"']+)["'][^>]*>/gi;
  return imgRegex.test(htmlContent);
}

/**
 * Extract image source paths from HTML content
 */
function extractImageSources(htmlContent: string): string[] {
  const imgRegex = /<img[^>]+src\s*=\s*["']([^"']+)["'][^>]*>/gi;
  const imageSources: string[] = [];
  let match;
  
  while ((match = imgRegex.exec(htmlContent)) !== null) {
    imageSources.push(match[1]);
  }
  
  return imageSources;
}

/**
 * POST /api/templates/upload - Upload template files
 */
export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();

    // Get form fields
    const templateName = formData.get('templateName') as string;
    const description = formData.get('description') as string;
    const layoutSize = formData.get('layoutSize') as string;
    const userId = formData.get('userId') as string;

    // Debug logging
    console.log('Template upload request:', {
      templateName,
      description,
      layoutSize,
      userId,
      hasHtmlFile: !!formData.get('htmlFile'),
      imageFilesCount: formData.getAll('imageFiles').length
    });
    
    // Get uploaded files
    const htmlFile = formData.get('htmlFile') as File;
    const imageFiles = formData.getAll('imageFiles') as File[];
    
    // Validate required fields
    if (!templateName || !htmlFile || !userId) {
      return NextResponse.json(
        { error: 'Template name, HTML file, and user ID are required' },
        { status: 400 }
      );
    }
    
    // Validate HTML file type
    if (!htmlFile.name.toLowerCase().endsWith('.htm') && !htmlFile.name.toLowerCase().endsWith('.html')) {
      return NextResponse.json(
        { error: 'Only HTML (.htm or .html) files are allowed' },
        { status: 400 }
      );
    }
    
    // Read HTML content
    const htmlContent = await htmlFile.text();
    
    // Validate HTML content (basic check)
    if (!htmlContent.toLowerCase().includes('<html') && !htmlContent.toLowerCase().includes('<!doctype')) {
      return NextResponse.json(
        { error: 'Invalid HTML file format' },
        { status: 400 }
      );
    }
    
    // Extract placeholders
    const placeholders = extractPlaceholders(htmlContent);
    
    // Check if images are required but not provided
    const hasImages = hasImageReferences(htmlContent);
    if (hasImages && imageFiles.length === 0) {
      return NextResponse.json(
        { 
          error: 'This HTML template contains image references. Please upload the associated image files.',
          requiresImages: true,
          imageSources: extractImageSources(htmlContent)
        },
        { status: 400 }
      );
    }
    
    // Create template directory structure
    const templatesDir = join(process.cwd(), 'public', 'templates');
    const templateDir = join(templatesDir, templateName);
    
    // Ensure directories exist
    if (!existsSync(templatesDir)) {
      await mkdir(templatesDir, { recursive: true });
    }
    
    if (!existsSync(templateDir)) {
      await mkdir(templateDir, { recursive: true });
    }
    
    // Save HTML file
    const htmlFileName = htmlFile.name;
    const htmlFilePath = join(templateDir, htmlFileName);
    const htmlBuffer = Buffer.from(await htmlFile.arrayBuffer());
    await writeFile(htmlFilePath, htmlBuffer);
    
    // Save image files if provided
    if (imageFiles.length > 0) {
      const imagesDir = join(templateDir, 'images');
      if (!existsSync(imagesDir)) {
        await mkdir(imagesDir, { recursive: true });
      }
      
      for (const imageFile of imageFiles) {
        if (imageFile.size > 0) { // Check if file is not empty
          const imageBuffer = Buffer.from(await imageFile.arrayBuffer());
          const imagePath = join(imagesDir, imageFile.name);
          await writeFile(imagePath, imageBuffer);
        }
      }
    }
    
    // Save template to database
    console.log('Saving template to database with params:', {
      templateName,
      description: description || undefined,
      htmlFileName,
      placeholders: JSON.stringify(placeholders),
      layoutSize: layoutSize || 'A4',
      userId: parseInt(userId)
    });

    const templateId = await createTemplate(
      templateName,
      description || undefined,
      htmlFileName,
      JSON.stringify(placeholders),
      layoutSize || 'A4',
      parseInt(userId)
    );

    console.log('Template saved successfully with ID:', templateId);
    
    return NextResponse.json(
      {
        message: 'Template uploaded successfully',
        templateId,
        templateName,
        placeholders,
        hasImages,
        imageCount: imageFiles.length
      },
      { status: 201 }
    );
    
  } catch (error) {
    console.error('Error uploading template:', error);
    return NextResponse.json(
      { error: 'Failed to upload template' },
      { status: 500 }
    );
  }
}
