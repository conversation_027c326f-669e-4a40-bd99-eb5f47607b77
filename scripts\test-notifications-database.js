#!/usr/bin/env node

/**
 * Database test script for LDIS Notifications
 * This script tests the notifications table functionality
 */

require("ts-node/register");

async function testNotificationsDatabaseOperations() {
  console.log("🧪 Testing Notifications Database Operations\n");

  try {
    // Import the database module
    const {
      initializeDatabase,
      createNotification,
      getAllNotifications,
      getNotificationsByUserId,
      getUnreadNotificationsByUserId,
      markNotificationAsRead,
      markAllNotificationsAsRead,
      getUnreadNotificationCount,
      getNotificationsWithInfo,
      deleteNotification,
      getAllUsers,
      getAllDocuments,
      createDocument,
      closeDatabase,
    } = require("../src/lib/database.ts");

    // Initialize the database
    console.log("📦 Initializing database...");
    await initializeDatabase();
    console.log("✅ Database initialized\n");

    // Check if users exist
    console.log("👥 Checking users...");
    const users = await getAllUsers();
    console.log(`Found ${users.length} users`);
    
    if (users.length === 0) {
      console.log("⚠️ No users found. You need to create a user first.");
      console.log("Run: node scripts/test-db.js\n");
      await closeDatabase();
      return;
    }

    const testUser = users[0];
    console.log(`Using test user: ${testUser.username} (ID: ${testUser.id})\n`);

    // Check if documents exist, create one if needed
    console.log("📄 Checking documents...");
    let documents = await getAllDocuments();
    console.log(`Found ${documents.length} documents`);

    let testDocument;
    if (documents.length === 0) {
      console.log("📝 Creating test document...");
      const documentId = await createDocument(
        "Test Document for Notifications",
        "John Doe",
        Buffer.from("Test document content"),
        testUser.id
      );
      documents = await getAllDocuments();
      testDocument = documents.find(d => d.id === documentId);
      console.log(`✅ Test document created with ID: ${documentId}\n`);
    } else {
      testDocument = documents[0];
      console.log(`Using existing document: ${testDocument.document_name} (ID: ${testDocument.id})\n`);
    }

    // Test creating notifications
    console.log("🔔 Testing notification creation...");
    const notificationId1 = await createNotification(
      testDocument.id,
      testDocument.document_name,
      testDocument.applicant_name,
      testUser.id
    );
    console.log(`✅ Notification 1 created with ID: ${notificationId1}`);

    const notificationId2 = await createNotification(
      testDocument.id,
      "Another Document",
      "Jane Smith",
      testUser.id
    );
    console.log(`✅ Notification 2 created with ID: ${notificationId2}\n`);

    // Test getting all notifications
    console.log("📋 Testing get all notifications...");
    const allNotifications = await getAllNotifications();
    console.log(`✅ Found ${allNotifications.length} total notifications\n`);

    // Test getting notifications by user ID
    console.log("👤 Testing get notifications by user ID...");
    const userNotifications = await getNotificationsByUserId(testUser.id);
    console.log(`✅ Found ${userNotifications.length} notifications for user ${testUser.username}\n`);

    // Test getting unread notifications
    console.log("📬 Testing get unread notifications...");
    const unreadNotifications = await getUnreadNotificationsByUserId(testUser.id);
    console.log(`✅ Found ${unreadNotifications.length} unread notifications\n`);

    // Test getting unread notification count
    console.log("🔢 Testing unread notification count...");
    const unreadCount = await getUnreadNotificationCount(testUser.id);
    console.log(`✅ Unread notification count: ${unreadCount}\n`);

    // Test marking notification as read
    console.log("✅ Testing mark notification as read...");
    await markNotificationAsRead(notificationId1);
    const unreadAfterMark = await getUnreadNotificationCount(testUser.id);
    console.log(`✅ Notification ${notificationId1} marked as read`);
    console.log(`✅ Unread count after marking one as read: ${unreadAfterMark}\n`);

    // Test getting notifications with user info (JOIN)
    console.log("🔗 Testing notifications with user info (JOIN)...");
    const notificationsWithInfo = await getNotificationsWithInfo();
    console.log(`✅ Found ${notificationsWithInfo.length} notifications with user info`);
    if (notificationsWithInfo.length > 0) {
      console.log(`   First notification: ${notificationsWithInfo[0].document_name} by ${notificationsWithInfo[0].username}\n`);
    }

    // Test marking all notifications as read
    console.log("✅ Testing mark all notifications as read...");
    await markAllNotificationsAsRead(testUser.id);
    const unreadAfterMarkAll = await getUnreadNotificationCount(testUser.id);
    console.log(`✅ All notifications marked as read`);
    console.log(`✅ Unread count after marking all as read: ${unreadAfterMarkAll}\n`);

    // Test deleting a notification
    console.log("🗑️ Testing delete notification...");
    await deleteNotification(notificationId2);
    const notificationsAfterDelete = await getAllNotifications();
    console.log(`✅ Notification ${notificationId2} deleted`);
    console.log(`✅ Notifications remaining: ${notificationsAfterDelete.length}\n`);

    console.log("🎉 Notifications table test completed successfully!");
    console.log("\nNotifications table features tested:");
    console.log("✅ Create notification");
    console.log("✅ Get all notifications");
    console.log("✅ Get notifications by user ID");
    console.log("✅ Get unread notifications");
    console.log("✅ Get unread notification count");
    console.log("✅ Mark notification as read");
    console.log("✅ Mark all notifications as read");
    console.log("✅ Get notifications with user info (JOIN)");
    console.log("✅ Delete notification");
    console.log("✅ Foreign key constraints (document_id -> documents.id, user_id -> users.id)");

    // Close the database connection
    await closeDatabase();
  } catch (error) {
    console.error("❌ Error testing notifications:", error);
    process.exit(1);
  }
}

// Run the test
testNotificationsDatabaseOperations();
