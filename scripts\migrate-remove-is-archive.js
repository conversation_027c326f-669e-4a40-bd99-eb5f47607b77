#!/usr/bin/env node

/**
 * Migration script to remove is_archive column from documents table
 * This script creates a new documents table without the is_archive column
 * and migrates existing data
 */

const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Database path
const dbPath = path.join(__dirname, '..', 'data', 'ldis.db');

console.log('🔄 Starting migration to remove is_archive column...');
console.log(`📁 Database path: ${dbPath}`);

// Open database connection
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ Error opening database:', err.message);
    process.exit(1);
  }
  console.log('✅ Connected to SQLite database');
});

// Run the migration
async function runMigration() {
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      // Step 1: Create new documents table without is_archive column
      console.log('📝 Creating new documents table structure...');
      db.run(`
        CREATE TABLE IF NOT EXISTS documents_new (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          document_name TEXT NOT NULL,
          applicant_name TEXT NOT NULL,
          uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          document_data BLOB,
          status TEXT DEFAULT 'pending',
          approved_at DATETIME,
          user_id INTEGER NOT NULL,
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )
      `, (err) => {
        if (err) {
          console.error('❌ Error creating new documents table:', err.message);
          reject(err);
          return;
        }
        console.log('✅ New documents table structure created');

        // Step 2: Copy data from old table to new table (excluding is_archive column)
        console.log('📋 Copying data from old table to new table...');
        db.run(`
          INSERT INTO documents_new (id, document_name, applicant_name, uploaded_at, document_data, status, approved_at, user_id)
          SELECT id, document_name, applicant_name, uploaded_at, document_data, status, approved_at, user_id
          FROM documents
        `, (err) => {
          if (err) {
            console.error('❌ Error copying data:', err.message);
            reject(err);
            return;
          }
          console.log('✅ Data copied successfully');

          // Step 3: Drop old table
          console.log('🗑️ Dropping old documents table...');
          db.run('DROP TABLE documents', (err) => {
            if (err) {
              console.error('❌ Error dropping old table:', err.message);
              reject(err);
              return;
            }
            console.log('✅ Old documents table dropped');

            // Step 4: Rename new table to original name
            console.log('🔄 Renaming new table to documents...');
            db.run('ALTER TABLE documents_new RENAME TO documents', (err) => {
              if (err) {
                console.error('❌ Error renaming table:', err.message);
                reject(err);
                return;
              }
              console.log('✅ Table renamed successfully');

              // Step 5: Verify the migration
              console.log('🔍 Verifying migration...');
              db.get('PRAGMA table_info(documents)', (err, row) => {
                if (err) {
                  console.error('❌ Error verifying migration:', err.message);
                  reject(err);
                  return;
                }

                // Check if is_archive column still exists
                db.all('PRAGMA table_info(documents)', (err, rows) => {
                  if (err) {
                    console.error('❌ Error getting table info:', err.message);
                    reject(err);
                    return;
                  }

                  const hasIsArchive = rows.some(row => row.name === 'is_archive');
                  if (hasIsArchive) {
                    console.log('❌ Migration failed: is_archive column still exists');
                    reject(new Error('Migration verification failed'));
                  } else {
                    console.log('✅ Migration verified: is_archive column successfully removed');
                    
                    // Show current table structure
                    console.log('\n📋 Current documents table structure:');
                    rows.forEach(row => {
                      console.log(`   ${row.name}: ${row.type}${row.notnull ? ' NOT NULL' : ''}${row.pk ? ' PRIMARY KEY' : ''}`);
                    });
                    
                    resolve();
                  }
                });
              });
            });
          });
        });
      });
    });
  });
}

// Execute migration
runMigration()
  .then(() => {
    console.log('\n🎉 Migration completed successfully!');
    console.log('The is_archive column has been removed from the documents table.');
    console.log('All existing document data has been preserved.');
    
    db.close((err) => {
      if (err) {
        console.error('❌ Error closing database:', err.message);
      } else {
        console.log('✅ Database connection closed');
      }
    });
  })
  .catch((error) => {
    console.error('\n❌ Migration failed:', error.message);
    
    db.close((err) => {
      if (err) {
        console.error('❌ Error closing database:', err.message);
      }
      process.exit(1);
    });
  });
