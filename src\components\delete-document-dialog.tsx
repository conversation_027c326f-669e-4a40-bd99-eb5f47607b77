"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON>ader2, <PERSON><PERSON><PERSON><PERSON><PERSON>, Trash2 } from "lucide-react";
import { toast } from "sonner";

interface Document {
  id: number;
  document_name: string;
  applicant_name: string;
  uploaded_at: string;
  document_data: Buffer | null;
  status: string;
  approved_at: string | null;
  user_id: number;
  is_archive: boolean;
}

interface DeleteDocumentDialogProps {
  document: Document | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onDocumentDeleted: (documentId: number) => void;
}

export function DeleteDocumentDialog({
  document,
  open,
  onOpenChange,
  onDocumentDeleted,
}: DeleteDocumentDialogProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  // Handle delete confirmation
  const handleDelete = async () => {
    if (!document) return;

    setLoading(true);
    setError("");

    try {
      const response = await fetch(`/api/documents/${document.id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to delete document");
      }

      // Notify parent component that document was deleted
      onDocumentDeleted(document.id);

      // Close the dialog
      onOpenChange(false);

      toast.success(
        `Document "${document.document_name}" deleted successfully`
      );
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to delete document";
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Handle dialog close
  const handleClose = () => {
    if (!loading) {
      onOpenChange(false);
      setError("");
    }
  };

  if (!document) return null;

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-destructive">
            <AlertTriangle className="h-5 w-5" />
            Delete Document
          </DialogTitle>
          <DialogDescription>
            This action cannot be undone. This will permanently delete the
            document from the system.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Document Details */}
          <div className="rounded-lg border p-4 space-y-2">
            <div className="flex justify-between">
              <span className="text-sm font-medium text-muted-foreground">
                Document Name:
              </span>
              <span className="text-sm font-medium">
                {document.document_name}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm font-medium text-muted-foreground">
                Applicant:
              </span>
              <span className="text-sm">{document.applicant_name}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm font-medium text-muted-foreground">
                Status:
              </span>
              <span className="text-sm capitalize">{document.status}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm font-medium text-muted-foreground">
                Uploaded:
              </span>
              <span className="text-sm">
                {new Date(document.uploaded_at).toLocaleDateString()}
              </span>
            </div>
          </div>

          {/* Warning Message */}
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <strong>Warning:</strong> Deleting this document will:
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li>Permanently remove the document from the database</li>
                <li>Delete all associated document data</li>
                <li>Make the document unavailable for review</li>
              </ul>
            </AlertDescription>
          </Alert>
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="button"
            variant="destructive"
            onClick={handleDelete}
            disabled={loading}
            className="flex items-center gap-2"
          >
            {loading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Trash2 className="h-4 w-4" />
            )}
            {loading ? "Deleting..." : "Delete Document"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
